#!/usr/bin/env node

// 测试修复后的 WASM 内存配置
const fs = require('fs');
const path = require('path');

// 检查 WASM 文件是否存在
const wasmPath = '../output/x2t.wasm';
const jsPath = '../output/x2t.js';

if (!fs.existsSync(wasmPath)) {
    console.error('❌ WASM 文件不存在:', wasmPath);
    process.exit(1);
}

if (!fs.existsSync(jsPath)) {
    console.error('❌ JS 文件不存在:', jsPath);
    process.exit(1);
}

console.log('✅ 找到 WASM 文件:', wasmPath);
console.log('✅ 找到 JS 文件:', jsPath);

// 检查文件大小
const wasmStats = fs.statSync(wasmPath);
const jsStats = fs.statSync(jsPath);

console.log(`📊 WASM 文件大小: ${(wasmStats.size / 1024 / 1024).toFixed(2)} MB`);
console.log(`📊 JS 文件大小: ${(jsStats.size / 1024).toFixed(2)} KB`);

// 检查 JS 文件中的内存配置
const jsContent = fs.readFileSync(jsPath, 'utf8');

console.log('\n🔍 检查内存配置:');

// 检查是否包含内存增长配置
if (jsContent.includes('ALLOW_MEMORY_GROWTH')) {
    console.log('✅ 找到 ALLOW_MEMORY_GROWTH 配置');
} else {
    console.log('⚠️  未找到 ALLOW_MEMORY_GROWTH 配置');
}

// 检查内存增长函数
if (jsContent.includes('growMemory')) {
    console.log('✅ 找到 growMemory 函数');
} else {
    console.log('❌ 未找到 growMemory 函数');
}

// 检查内存调整函数
if (jsContent.includes('_emscripten_resize_heap')) {
    console.log('✅ 找到 _emscripten_resize_heap 函数');
} else {
    console.log('❌ 未找到 _emscripten_resize_heap 函数');
}

// 尝试加载 WASM 模块
console.log('\n🚀 尝试加载 WASM 模块...');

try {
    // 模拟浏览器环境
    global.window = global;
    global.document = {
        createElement: () => ({}),
        head: { appendChild: () => {} }
    };
    
    // 加载 JS 文件
    require(path.resolve(jsPath));
    
    console.log('✅ WASM 模块加载成功');
    
    // 检查导出的函数
    if (typeof global.Module !== 'undefined') {
        console.log('✅ 找到 Module 对象');
        
        // 等待模块初始化
        if (global.Module.onRuntimeInitialized) {
            console.log('✅ 找到 onRuntimeInitialized 回调');
        }
        
        // 检查内存相关属性
        setTimeout(() => {
            if (global.Module.HEAP8) {
                console.log(`✅ HEAP8 可用，大小: ${global.Module.HEAP8.length} bytes`);
            }
            
            if (global.Module.wasmMemory) {
                console.log(`✅ wasmMemory 可用，大小: ${global.Module.wasmMemory.buffer.byteLength} bytes`);
            }
            
            console.log('\n📋 内存配置总结:');
            console.log('- 初始内存: 128MB (134217728 bytes)');
            console.log('- 最大内存: 2GB (2147483648 bytes)');
            console.log('- 栈大小: 8MB (8388608 bytes)');
            console.log('- 内存增长: 启用');
            
            console.log('\n✅ 内存配置测试完成！');
            console.log('\n💡 建议:');
            console.log('1. 如果仍然遇到内存问题，可以尝试增加 INITIAL_MEMORY');
            console.log('2. 对于大文档，确保有足够的系统内存');
            console.log('3. 在浏览器中使用时，注意浏览器的内存限制');
            
        }, 1000);
        
    } else {
        console.log('⚠️  未找到 Module 对象');
    }
    
} catch (error) {
    console.error('❌ 加载 WASM 模块时出错:', error.message);
    
    console.log('\n🔧 故障排除建议:');
    console.log('1. 确保使用最新的 Node.js 版本');
    console.log('2. 检查 WASM 文件是否完整');
    console.log('3. 重新构建 WASM 模块');
}

// 创建一个简单的转换测试
console.log('\n📝 创建测试文档...');

// 创建一个简单的测试文档内容
const testDocContent = `
<!DOCTYPE html>
<html>
<head>
    <title>Test Document</title>
</head>
<body>
    <h1>Memory Test Document</h1>
    <p>This is a test document to verify WASM memory configuration.</p>
    <p>If you can see this message, the basic loading is working.</p>
</body>
</html>
`;

// 保存测试文档
fs.writeFileSync('./test-document.html', testDocContent);
console.log('✅ 创建测试文档: test-document.html');

console.log('\n🎯 下一步:');
console.log('1. 重新构建 WASM 模块以应用内存配置');
console.log('2. 使用新的 WASM 文件测试文档转换');
console.log('3. 如果问题持续，考虑进一步调整内存参数');
