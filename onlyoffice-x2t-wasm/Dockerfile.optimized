# 优化版 Dockerfile - 使用预构建的基础镜像
# 如果基础镜像不存在，会自动构建
ARG BASE_IMAGE=onlyoffice-x2t-base:latest

# 尝试使用预构建的基础镜像，如果不存在则构建
FROM ${BASE_IMAGE} AS base-prebuilt
# 如果上面失败，使用本地构建的基础镜像
FROM ubuntu:22.04 AS base-fallback

SHELL ["/bin/bash", "-c"]

# 代理配置 - 可以通过构建参数传入
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG NO_PROXY
ARG http_proxy
ARG https_proxy
ARG no_proxy

# 设置环境变量
ENV HTTP_PROXY=${HTTP_PROXY}
ENV HTTPS_PROXY=${HTTPS_PROXY}
ENV NO_PROXY=${NO_PROXY}
ENV http_proxy=${http_proxy}
ENV https_proxy=${https_proxy}
ENV no_proxy=${no_proxy}

# 配置 apt 代理（如果设置了代理）
RUN if [ -n "$HTTP_PROXY" ] || [ -n "$http_proxy" ]; then \
        echo "Acquire::http::Proxy \"${HTTP_PROXY:-$http_proxy}\";" > /etc/apt/apt.conf.d/01proxy && \
        echo "Acquire::https::Proxy \"${HTTPS_PROXY:-$https_proxy}\";" >> /etc/apt/apt.conf.d/01proxy; \
    fi

# 安装系统依赖
RUN --mount=target=/var/lib/apt/lists,type=cache,sharing=locked \
    --mount=target=/var/cache/apt,type=cache,sharing=locked \
    apt update \
    && apt install -y \
       git \
       python-is-python3 \
       xz-utils \
       lbzip2 \
       automake \
       libtool \
       autoconf \
       make \
       qt6-base-dev \
       build-essential \
       cmake \
       zip \
       pkg-config \
       wget \
       curl \
       ca-certificates \
    && apt clean

# 配置 git 代理（在安装 git 之后）
RUN if [ -n "$HTTP_PROXY" ] || [ -n "$http_proxy" ]; then \
        git config --global http.proxy "${HTTP_PROXY:-$http_proxy}" && \
        git config --global https.proxy "${HTTPS_PROXY:-$https_proxy}"; \
    fi

# 安装和配置 Emscripten
WORKDIR /
RUN git clone https://github.com/emscripten-core/emsdk.git
WORKDIR /emsdk
ARG emversion=4.0.11
RUN git fetch -a && git checkout $emversion
RUN ./emsdk install $emversion
RUN ./emsdk activate $emversion

# 配置 Qt
RUN . /emsdk/emsdk_env.sh && qtchooser -install qt6 $(which qmake6)
ENV QT_SELECT=qt6

# 复制构建脚本
WORKDIR /
COPY embuild.sh /bin/embuild.sh
RUN chmod +x /bin/embuild.sh

# 选择使用哪个基础镜像
FROM base-prebuilt AS base
# 如果预构建镜像不可用，注释上面一行，取消注释下面一行
# FROM base-fallback AS base

# 从这里开始是原来的构建逻辑，但现在基于稳定的基础镜像
# TODO remove?
FROM base AS freetype
COPY core/DesktopEditor/freetype-2.10.4 /freetype
WORKDIR /freetype
RUN bash ./autogen.sh
RUN . /emsdk/emsdk_env.sh \
 && emconfigure ./configure
RUN . /emsdk/emsdk_env.sh \
 && emmake make
RUN . /emsdk/emsdk_env.sh \
 && emmake make install

# 继续其他构建阶段...
# (这里可以继续添加原 Dockerfile 的其他部分)
