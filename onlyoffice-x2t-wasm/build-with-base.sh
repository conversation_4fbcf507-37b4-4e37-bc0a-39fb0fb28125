#!/bin/bash

# 分层构建脚本 - 先构建基础镜像，再构建应用
set -e

# 默认代理配置
HTTP_PROXY_ARG="${HTTP_PROXY:-http://host.docker.internal:12334}"
HTTPS_PROXY_ARG="${HTTPS_PROXY:-http://host.docker.internal:12334}"
NO_PROXY_ARG="${NO_PROXY:-localhost,127.0.0.1,.local}"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --http-proxy)
            HTTP_PROXY_ARG="$2"
            shift 2
            ;;
        --https-proxy)
            HTTPS_PROXY_ARG="$2"
            shift 2
            ;;
        --no-proxy)
            NO_PROXY_ARG="$2"
            shift 2
            ;;
        --rebuild-base)
            REBUILD_BASE=true
            shift
            ;;
        --skip-base)
            SKIP_BASE=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --http-proxy URL     HTTP 代理"
            echo "  --https-proxy URL    HTTPS 代理"
            echo "  --no-proxy HOSTS     不使用代理的主机"
            echo "  --rebuild-base       强制重建基础镜像"
            echo "  --skip-base          跳过基础镜像构建"
            echo "  -h, --help           显示帮助"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            exit 1
            ;;
    esac
done

echo "=== 分层构建 OnlyOffice X2T WASM ==="
echo "HTTP 代理:  $HTTP_PROXY_ARG"
echo "HTTPS 代理: $HTTPS_PROXY_ARG"
echo "NO_PROXY:   $NO_PROXY_ARG"
echo "=================================="

# 构建参数
BUILD_ARGS="--build-arg HTTP_PROXY=$HTTP_PROXY_ARG"
BUILD_ARGS="$BUILD_ARGS --build-arg HTTPS_PROXY=$HTTPS_PROXY_ARG"
BUILD_ARGS="$BUILD_ARGS --build-arg http_proxy=$HTTP_PROXY_ARG"
BUILD_ARGS="$BUILD_ARGS --build-arg https_proxy=$HTTPS_PROXY_ARG"
BUILD_ARGS="$BUILD_ARGS --build-arg NO_PROXY=$NO_PROXY_ARG"
BUILD_ARGS="$BUILD_ARGS --build-arg no_proxy=$NO_PROXY_ARG"

# 检查基础镜像是否存在
BASE_IMAGE_EXISTS=$(docker images -q onlyoffice-x2t-base:latest)

if [ -z "$SKIP_BASE" ]; then
    if [ -n "$REBUILD_BASE" ] || [ -z "$BASE_IMAGE_EXISTS" ]; then
        echo ""
        echo "🏗️  构建基础镜像..."
        echo "这个步骤包含所有系统依赖和 Emscripten，只需要构建一次"
        echo ""
        
        docker build \
            $BUILD_ARGS \
            -f Dockerfile.base \
            -t onlyoffice-x2t-base:latest \
            .
        
        if [ $? -eq 0 ]; then
            echo "✅ 基础镜像构建成功"
        else
            echo "❌ 基础镜像构建失败"
            exit 1
        fi
    else
        echo "✅ 基础镜像已存在，跳过构建"
    fi
else
    echo "⏭️  跳过基础镜像构建"
fi

echo ""
echo "🚀 构建应用镜像..."
echo "这个步骤会很快，因为基础环境已经准备好了"
echo ""

# 构建应用镜像，使用预构建的基础镜像
docker build \
    $BUILD_ARGS \
    --build-arg BASE_IMAGE=onlyoffice-x2t-base:latest \
    --target output \
    -t onlyoffice-x2t-wasm:latest \
    .

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 构建完成！"
    echo ""
    echo "📦 镜像信息:"
    docker images | grep -E "(onlyoffice-x2t-base|onlyoffice-x2t-wasm)"
    
    echo ""
    echo "📋 下一步:"
    echo "1. 提取构建产物:"
    echo "   docker create --name temp-container onlyoffice-x2t-wasm:latest"
    echo "   docker cp temp-container:/ ./output/"
    echo "   docker rm temp-container"
    echo ""
    echo "2. 测试 WASM 模块:"
    echo "   在浏览器中打开 output/memory-test.html"
    echo ""
    echo "💡 提示:"
    echo "- 基础镜像 onlyoffice-x2t-base:latest 可以重复使用"
    echo "- 下次构建时会自动使用基础镜像，大大加快速度"
    echo "- 如需更新基础镜像，使用 --rebuild-base 参数"
else
    echo "❌ 应用镜像构建失败"
    exit 1
fi
