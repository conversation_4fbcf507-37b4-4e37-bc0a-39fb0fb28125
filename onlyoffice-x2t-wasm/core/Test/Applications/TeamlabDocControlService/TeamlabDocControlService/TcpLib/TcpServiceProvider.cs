/*
 * (c) Copyright Ascensio System SIA 2010-2023
 *
 * This program is a free software product. You can redistribute it and/or
 * modify it under the terms of the GNU Affero General Public License (AGPL)
 * version 3 as published by the Free Software Foundation. In accordance with
 * Section 7(a) of the GNU AGPL its Section 15 shall be amended to the effect
 * that Ascensio System SIA expressly excludes the warranty of non-infringement
 * of any third-party rights.
 *
 * This program is distributed WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR  PURPOSE. For
 * details, see the GNU AGPL at: http://www.gnu.org/licenses/agpl-3.0.html
 *
 * You can contact Ascensio System SIA at 20A-6 Ernesta Birznieka-Upish
 * street, Riga, Latvia, EU, LV-1050.
 *
 * The  interactive user interfaces in modified source and object code versions
 * of the Program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU AGPL version 3.
 *
 * Pursuant to Section 7(b) of the License you must retain the original Product
 * logo when distributing the program. Pursuant to Section 7(e) we decline to
 * grant you any rights under trademark law for use of our trademarks.
 *
 * All the Product's GUI elements, including illustrations and icon sets, as
 * well as technical writing content are licensed under the terms of the
 * Creative Commons Attribution-ShareAlike 4.0 International. See the License
 * terms at http://creativecommons.org/licenses/by-sa/4.0/legalcode
 *
 */
using System;
using System.Text;
using TcpLib;

namespace TeamlabDocControlService.TcpServer
{
    public delegate string ControlTcpRequestEventHandler (string requestXml);

	/// <SUMMARY>
    /// ControlServiceProvider. It handles requests from client aspx code and returns a xml to it (realizes IPC mechanism)
	/// </SUMMARY>
	public class ServiceProvider: TcpServiceProvider
	{
        public event ControlTcpRequestEventHandler Requested;

		private string _receivedStr;

		public override object Clone()
		{
            ServiceProvider provider = new ServiceProvider();
            if (null != Requested)
            {
                provider.Requested = Requested.Clone() as ControlTcpRequestEventHandler;
            }

            return provider;
		}

		public override void OnAcceptConnection(ConnectionState state)
		{
			_receivedStr = "";
			if(!state.Write(Encoding.UTF8.GetBytes("TLDCS_HELLO\r\n"), 0, 13))
				state.EndConnection(); //if write fails... then close connection
		}


		public override void OnReceiveData(ConnectionState state)
		{
            
			while(state.AvailableData > 0)
			{
                byte[] buffer = new byte[state.AvailableData];
                int readBytes = state.Read(buffer, 0, state.AvailableData);
                if (readBytes > 0)
                {
                    _receivedStr += Encoding.UTF8.GetString(buffer, 0, readBytes);
                }
                else
                {
                    state.EndConnection(); //If read fails then close connection
                    return;
                }
			}
            
            //
            if (_receivedStr.Length > 0)
            {
                // parse string ()
                string sRequest = _receivedStr;
                _receivedStr = "";

                string sResponse = "";
                if (null != Requested)
                    sResponse = Requested(sRequest);

                if (sResponse.Length > 0)
                {
                    // send response
                    state.Write(Encoding.UTF8.GetBytes(sResponse), 0, sResponse.Length);
                }                                
            }
		}


		public override void OnDropConnection(ConnectionState state)
		{
			//Nothing to clean here
		}
	}
}
