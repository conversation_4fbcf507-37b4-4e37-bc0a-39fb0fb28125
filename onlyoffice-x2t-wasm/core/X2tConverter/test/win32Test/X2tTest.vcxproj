﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{355A22F4-1394-4B82-B2F1-FF0ECFB9E3EF}</ProjectGuid>
    <RootNamespace>X2tTest</RootNamespace>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>14.0.23107.0</_ProjectFileVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\</IntDir>
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>..\..\..\Common\3dParty\boost\build\win_32\include;$(IncludePath)</IncludePath>
    <LibraryPath>..\..\..\Common\3dParty\boost\build\win_32\lib;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>../../../build/lib/win_64/debug/</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>..\..\..\Common\3dParty\boost\build\win_64\include;$(IncludePath)</IncludePath>
    <LibraryPath>..\..\..\Common\3dParty\boost\build\win_64\lib;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\</IntDir>
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>..\..\..\Common\3dParty\boost\build\win_32\include;$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>..\..\..\Common\3dParty\boost\build\win_32\lib;$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(SolutionDir)$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(IncludePath)</AdditionalIncludeDirectories>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader />
      <ProgramDataBaseFileName>$(IntDir)X2tTestD.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <PreprocessorDefinitions>DONT_WRITE_EMBEDDED_FONTS;_RWSTD_NO_SETRLIMIT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <TargetEnvironment>X64</TargetEnvironment>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <MinimalRebuild>false</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader />
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <PreprocessorDefinitions>DONT_WRITE_EMBEDDED_FONTS;_RWSTD_NO_SETRLIMIT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <TargetMachine>MachineX64</TargetMachine>
      <AdditionalOptions>/INCREMENTAL:NO %(AdditionalOptions)</AdditionalOptions>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>DONT_WRITE_EMBEDDED_FONTS;_RWSTD_NO_SETRLIMIT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <PrecompiledHeader />
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(IncludePath)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <TargetEnvironment>X64</TargetEnvironment>
    </Midl>
    <ClCompile>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;FILE_FORMAT_CHECKER_WITH_MACRO;_USE_LIBXML2_READER_;LIBXML_READER_ENABLED;USE_LITE_READER;_USE_XMLLITE_READER_;DONT_WRITE_EMBEDDED_FONTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <PrecompiledHeader />
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\Common\3dParty\pole\pole.cpp" />
    <ClCompile Include="..\..\..\Common\OfficeFileFormatChecker2.cpp" />
    <ClCompile Include="..\..\src\ASCConverters.cpp" />
    <ClCompile Include="..\..\src\cextracttools.cpp" />
    <ClCompile Include="X2tTest.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\src\ASCConverters.h" />
    <ClInclude Include="..\..\src\cextracttools.h" />
    <ClInclude Include="..\..\src\lib\common.h" />
    <ClInclude Include="..\..\src\lib\crypt.h" />
    <ClInclude Include="..\..\src\lib\csv.h" />
    <ClInclude Include="..\..\src\lib\doc.h" />
    <ClInclude Include="..\..\src\lib\docx.h" />
    <ClInclude Include="..\..\src\lib\html.h" />
    <ClInclude Include="..\..\src\lib\hwp.h" />
    <ClInclude Include="..\..\src\lib\iwork.h" />
    <ClInclude Include="..\..\src\lib\odf.h" />
    <ClInclude Include="..\..\src\lib\pdf_image.h" />
    <ClInclude Include="..\..\src\lib\pdf_oform.h" />
    <ClInclude Include="..\..\src\lib\ppt.h" />
    <ClInclude Include="..\..\src\lib\pptx.h" />
    <ClInclude Include="..\..\src\lib\rtf.h" />
    <ClInclude Include="..\..\src\lib\txt.h" />
    <ClInclude Include="..\..\src\lib\xls.h" />
    <ClInclude Include="..\..\src\lib\xlsx.h" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Common\cfcpp\CompoundFileLib.vcxproj">
      <Project>{fa22bab4-e93e-459d-8a5f-16764fbbed40}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\MsBinaryFile\Projects\DocFormatLib\Windows\DocFormatLib.vcxproj">
      <Project>{c5371405-338f-4b70-83bd-2a5cdf64f383}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\MsBinaryFile\Projects\PPTFormatLib\Windows\PPTFormatLib.vcxproj">
      <Project>{7b27e40e-f70a-4a74-a77c-0944d7931d15}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\MsBinaryFile\Projects\VbaFormatLib\Windows\VbaFormat.vcxproj">
      <Project>{041dd428-2d5c-4d97-8ab7-7207f3d5b801}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\MsBinaryFile\Projects\XlsFormatLib\Windows\XlsFormat.vcxproj">
      <Project>{77ddc8d7-5b12-4ff2-9629-26aebca8436d}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\MsBinaryFile\Projects\XlsFormatLib\Windows\XlsXlsxConverter.vcxproj">
      <Project>{cbedd0d1-10a8-45c1-af81-8492f40964ca}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\OdfFile\Projects\Windows\cpcommon.vcxproj">
      <Project>{609ed938-3ca8-4bed-b363-25096d4c4812}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\OdfFile\Projects\Windows\cpodf.vcxproj">
      <Project>{50e20601-4a8d-4afb-8870-63828d328429}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\OdfFile\Projects\Windows\cpxml.vcxproj">
      <Project>{41bed424-4eaf-4053-8a5f-1e2a387d53d1}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\OdfFile\Projects\Windows\formulasconvert.vcxproj">
      <Project>{94954a67-a853-43b1-a727-6ef2774c5a6a}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\OdfFile\Projects\Windows\OdfFormatW.vcxproj">
      <Project>{e5a67556-44da-4481-8f87-0a3aedbd20dd}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\OdfFile\Projects\Windows\Oox2OdfConverter.vcxproj">
      <Project>{bee01b53-244a-44e6-8947-ed9342d9247e}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\OOXML\Projects\Windows\BinaryFormatLib\BinaryFormatLib.vcxproj">
      <Project>{cd359215-e183-4ea7-b986-42868b10d8b8}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\OOXML\Projects\Windows\DocxFormatLib\DocxFormatLib.vcxproj">
      <Project>{a100103a-353e-45e8-a9b8-90b87cc5c0b0}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\OOXML\Projects\Windows\PPTXFormatLib\PPTXFormat.vcxproj">
      <Project>{36636678-ae25-4be6-9a34-2561d1bcf302}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\OOXML\Projects\Windows\XlsbFormatLib\XlsbFormatLib.vcxproj">
      <Project>{13e13907-49da-482e-ad58-026d06a5cd11}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\RtfFile\Projects\Windows\Win32\RtfFormatLib.vcxproj">
      <Project>{af2d00a7-a351-4700-ae88-c1d9ade29345}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\TxtFile\Projects\Windows\TxtXmlFormatLib.vcxproj">
      <Project>{dacbe6ca-e089-47d1-8ce7-c7db59c15417}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>