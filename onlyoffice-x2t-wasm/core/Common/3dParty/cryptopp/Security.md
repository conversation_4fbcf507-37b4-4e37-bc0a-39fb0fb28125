# Security Policy

## Supported Versions

We support modern versions of the Crypto++ library. Modern versions include the tip of Master and the latest release.

We also support versions of the library supplied by distributions such as Debian, Fedora, Red Hat and Ubuntu. We don't leave distros unsupported simply because we have released a new version of the library. And we don't expect a package maintainer to fix our bugs for us.

## Reporting a Vulnerability

You can report a security related bug in the [GitHub bug tracker](https://github.com/weidai11/cryptopp) or at the [mailing list](https://groups.google.com/g/cryptopp-users).

If we receive a report of a security related bug then we will ensure a Github issue is opened and we will make an announcement on the mailing list. If you corresponded by private email then we will open the Github issue and make the announcement.

All information will be made public. We do not withhold information from users because stake holders need accurate information to access risk and place controls to remediate the risk.
