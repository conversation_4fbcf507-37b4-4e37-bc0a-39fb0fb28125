﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DLL-Import Debug|Win32">
      <Configuration>DLL-Import Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DLL-Import Debug|x64">
      <Configuration>DLL-Import Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DLL-Import Release|Win32">
      <Configuration>DLL-Import Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DLL-Import Release|x64">
      <Configuration>DLL-Import Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{3423EC9A-52E4-4A4D-9753-EDEBC38785EF}</ProjectGuid>
    <RootNamespace>cryptlib</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>14.0.23107.0</_ProjectFileVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(Platform)\Output\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(Platform)\Output\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Release|Win32'">
    <OutDir>$(Platform)\DLL_Output\Release\</OutDir>
    <IntDir>$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Release|x64'">
    <OutDir>$(Platform)\DLL_Output\Release\</OutDir>
    <IntDir>$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(Platform)\Output\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
    <IncludePath>$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(Platform)\Output\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);..\..\..\Common\3dParty\boost\build\win_64\include;</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64;..\..\..\Common\3dParty\boost\build\win_64\lib;</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Debug|Win32'">
    <OutDir>$(Platform)\DLL_Output\Debug\</OutDir>
    <IntDir>$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Debug|x64'">
    <OutDir>$(Platform)\DLL_Output\Debug\</OutDir>
    <IntDir>$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <OmitFramePointers>true</OmitFramePointers>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader />
      <PrecompiledHeaderFile />
      <ProgramDataBaseFileName>$(OutDir)vc80.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level4</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <OmitFramePointers>true</OmitFramePointers>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <ProgramDataBaseFileName>$(OutDir)vc80.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level4</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Release|Win32'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <OmitFramePointers>true</OmitFramePointers>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32;CRYPTOPP_IMPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <ProgramDataBaseFileName>$(OutDir)vc80.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level4</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Release|x64'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <OmitFramePointers>true</OmitFramePointers>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32;CRYPTOPP_IMPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <ProgramDataBaseFileName>$(OutDir)vc80.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level4</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_DEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32;CRYPTOPP_DISABLE_ASM;DISABLE_TYPE_MISMATCH;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <PrecompiledHeader />
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ProgramDataBaseFileName>$(IntDir)vc80.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4005;4311;4312;%(DisableSpecificWarnings)</DisableSpecificWarnings>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_DEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32;CRYPTOPP_DISABLE_ASM;DISABLE_TYPE_MISMATCH;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <ProgramDataBaseFileName>$(OutDir)vc80.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level4</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <WholeProgramOptimization>false</WholeProgramOptimization>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_DEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32;CRYPTOPP_IMPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <ProgramDataBaseFileName>$(OutDir)vc80.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level4</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DLL-Import Debug|x64'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_DEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32;CRYPTOPP_IMPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <ProgramDataBaseFileName>$(OutDir)vc80.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level4</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="3way.cpp" />
    <ClCompile Include="adler32.cpp" />
    <ClCompile Include="algebra.cpp" />
    <ClCompile Include="algparam.cpp" />
    <ClCompile Include="arc4.cpp" />
    <ClCompile Include="aria-simd.cpp" />
    <ClCompile Include="aria.cpp" />
    <ClCompile Include="ariatab.cpp" />
    <ClCompile Include="asn.cpp" />
    <ClCompile Include="authenc.cpp" />
    <ClCompile Include="base32.cpp" />
    <ClCompile Include="base64.cpp" />
    <ClCompile Include="basecode.cpp" />
    <ClCompile Include="bench1.cpp" />
    <ClCompile Include="bench2.cpp" />
    <ClCompile Include="bfinit.cpp" />
    <ClCompile Include="blake2-simd.cpp" />
    <ClCompile Include="blake2.cpp" />
    <ClCompile Include="blowfish.cpp" />
    <ClCompile Include="blumshub.cpp" />
    <ClCompile Include="camellia.cpp" />
    <ClCompile Include="cast.cpp" />
    <ClCompile Include="casts.cpp" />
    <ClCompile Include="cbcmac.cpp" />
    <ClCompile Include="ccm.cpp" />
    <ClCompile Include="chacha.cpp" />
    <ClCompile Include="channels.cpp" />
    <ClCompile Include="cmac.cpp" />
    <ClCompile Include="cpu.cpp" />
    <ClCompile Include="crc-simd.cpp" />
    <ClCompile Include="crc.cpp" />
    <ClCompile Include="cryptlib.cpp" />
    <ClCompile Include="datatest.cpp" />
    <ClCompile Include="default.cpp" />
    <ClCompile Include="des.cpp" />
    <ClCompile Include="dessp.cpp" />
    <ClCompile Include="dh.cpp" />
    <ClCompile Include="dh2.cpp" />
    <ClCompile Include="dll.cpp" />
    <ClCompile Include="dlltest.cpp" />
    <ClCompile Include="dsa.cpp" />
    <ClCompile Include="eax.cpp" />
    <ClCompile Include="ec2n.cpp" />
    <ClCompile Include="eccrypto.cpp" />
    <ClCompile Include="ecp.cpp" />
    <ClCompile Include="elgamal.cpp" />
    <ClCompile Include="emsa2.cpp" />
    <ClCompile Include="eprecomp.cpp" />
    <ClCompile Include="esign.cpp" />
    <ClCompile Include="files.cpp" />
    <ClCompile Include="filters.cpp" />
    <ClCompile Include="fips140.cpp" />
    <ClCompile Include="fipsalgt.cpp" />
    <ClCompile Include="fipstest.cpp" />
    <ClCompile Include="gcm-simd.cpp" />
    <ClCompile Include="gcm.cpp" />
    <ClCompile Include="gf256.cpp" />
    <ClCompile Include="gf2n.cpp" />
    <ClCompile Include="gf2_32.cpp" />
    <ClCompile Include="gfpcrypt.cpp" />
    <ClCompile Include="gost.cpp" />
    <ClCompile Include="gzip.cpp" />
    <ClCompile Include="hex.cpp" />
    <ClCompile Include="hmac.cpp" />
    <ClCompile Include="hrtimer.cpp" />
    <ClCompile Include="ida.cpp" />
    <ClCompile Include="idea.cpp" />
    <ClCompile Include="integer.cpp" />
    <ClCompile Include="iterhash.cpp" />
    <ClCompile Include="kalyna.cpp" />
    <ClCompile Include="kalynatab.cpp" />
    <ClCompile Include="keccak.cpp" />
    <ClCompile Include="luc.cpp" />
    <ClCompile Include="mars.cpp" />
    <ClCompile Include="marss.cpp" />
    <ClCompile Include="md2.cpp" />
    <ClCompile Include="md4.cpp" />
    <ClCompile Include="md5.cpp" />
    <ClCompile Include="misc.cpp" />
    <ClCompile Include="modes.cpp" />
    <ClCompile Include="mqueue.cpp" />
    <ClCompile Include="mqv.cpp" />
    <ClCompile Include="nbtheory.cpp" />
    <ClCompile Include="neon-simd.cpp" />
    <ClCompile Include="network.cpp" />
    <ClCompile Include="oaep.cpp" />
    <ClCompile Include="osrng.cpp" />
    <ClCompile Include="padlkrng.cpp" />
    <ClCompile Include="panama.cpp" />
    <ClCompile Include="pch.cpp" />
    <ClCompile Include="pkcspad.cpp" />
    <ClCompile Include="poly1305.cpp" />
    <ClCompile Include="polynomi.cpp" />
    <ClCompile Include="ppc-simd.cpp" />
    <ClCompile Include="pssr.cpp" />
    <ClCompile Include="pubkey.cpp" />
    <ClCompile Include="queue.cpp" />
    <ClCompile Include="rabin.cpp" />
    <ClCompile Include="randpool.cpp" />
    <ClCompile Include="rc2.cpp" />
    <ClCompile Include="rc5.cpp" />
    <ClCompile Include="rc6.cpp" />
    <ClCompile Include="rdrand.cpp" />
    <ClCompile Include="rdtables.cpp" />
    <ClCompile Include="regtest1.cpp" />
    <ClCompile Include="regtest2.cpp" />
    <ClCompile Include="regtest3.cpp" />
    <ClCompile Include="rijndael-simd.cpp" />
    <ClCompile Include="rijndael.cpp" />
    <ClCompile Include="ripemd.cpp" />
    <ClCompile Include="rng.cpp" />
    <ClCompile Include="rsa.cpp" />
    <ClCompile Include="rw.cpp" />
    <ClCompile Include="safer.cpp" />
    <ClCompile Include="salsa.cpp" />
    <ClCompile Include="scrypt.cpp" />
    <ClCompile Include="seal.cpp" />
    <ClCompile Include="seed.cpp" />
    <ClCompile Include="serpent.cpp" />
    <ClCompile Include="sha-simd.cpp" />
    <ClCompile Include="sha.cpp" />
    <ClCompile Include="sha3.cpp" />
    <ClCompile Include="shacal2-simd.cpp" />
    <ClCompile Include="shacal2.cpp" />
    <ClCompile Include="shark.cpp" />
    <ClCompile Include="sharkbox.cpp" />
    <ClCompile Include="simon-simd.cpp" />
    <ClCompile Include="simon.cpp" />
    <ClCompile Include="simple.cpp" />
    <ClCompile Include="skipjack.cpp" />
    <ClCompile Include="sm3.cpp" />
    <ClCompile Include="sm4.cpp" />
    <ClCompile Include="socketft.cpp" />
    <ClCompile Include="sosemanuk.cpp" />
    <ClCompile Include="speck-simd.cpp" />
    <ClCompile Include="speck.cpp" />
    <ClCompile Include="square.cpp" />
    <ClCompile Include="squaretb.cpp" />
    <ClCompile Include="sse-simd.cpp" />
    <ClCompile Include="strciphr.cpp" />
    <ClCompile Include="tea.cpp" />
    <ClCompile Include="test.cpp" />
    <ClCompile Include="tftables.cpp" />
    <ClCompile Include="threefish.cpp" />
    <ClCompile Include="tiger.cpp" />
    <ClCompile Include="tigertab.cpp" />
    <ClCompile Include="trdlocal.cpp" />
    <ClCompile Include="ttmac.cpp" />
    <ClCompile Include="tweetnacl.cpp" />
    <ClCompile Include="twofish.cpp" />
    <ClCompile Include="validat0.cpp" />
    <ClCompile Include="validat1.cpp" />
    <ClCompile Include="validat2.cpp" />
    <ClCompile Include="validat3.cpp" />
    <ClCompile Include="validat4.cpp" />
    <ClCompile Include="vmac.cpp" />
    <ClCompile Include="wait.cpp" />
    <ClCompile Include="wake.cpp" />
    <ClCompile Include="whrlpool.cpp" />
    <ClCompile Include="winpipes.cpp" />
    <ClCompile Include="xtr.cpp" />
    <ClCompile Include="xtrcrypt.cpp" />
    <ClCompile Include="zdeflate.cpp" />
    <ClCompile Include="zinflate.cpp" />
    <ClCompile Include="zlib.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="3way.h" />
    <ClInclude Include="adler32.h" />
    <ClInclude Include="adv-simd.h" />
    <ClInclude Include="aes.h" />
    <ClInclude Include="algebra.h" />
    <ClInclude Include="algparam.h" />
    <ClInclude Include="arc4.h" />
    <ClInclude Include="argnames.h" />
    <ClInclude Include="aria.h" />
    <ClInclude Include="asn.h" />
    <ClInclude Include="authenc.h" />
    <ClInclude Include="base32.h" />
    <ClInclude Include="base64.h" />
    <ClInclude Include="basecode.h" />
    <ClInclude Include="bench.h" />
    <ClInclude Include="blake2.h" />
    <ClInclude Include="blowfish.h" />
    <ClInclude Include="blumshub.h" />
    <ClInclude Include="camellia.h" />
    <ClInclude Include="cast.h" />
    <ClInclude Include="cbcmac.h" />
    <ClInclude Include="ccm.h" />
    <ClInclude Include="chacha.h" />
    <ClInclude Include="channels.h" />
    <ClInclude Include="cmac.h" />
    <ClInclude Include="config.h" />
    <ClInclude Include="cpu.h" />
    <ClInclude Include="crc.h" />
    <ClInclude Include="cryptlib.h" />
    <ClInclude Include="default.h" />
    <ClInclude Include="des.h" />
    <ClInclude Include="dh.h" />
    <ClInclude Include="dh2.h" />
    <ClInclude Include="dll.h" />
    <ClInclude Include="dmac.h" />
    <ClInclude Include="drbg.h" />
    <ClInclude Include="dsa.h" />
    <ClInclude Include="eax.h" />
    <ClInclude Include="ec2n.h" />
    <ClInclude Include="eccrypto.h" />
    <ClInclude Include="ecp.h" />
    <ClInclude Include="ecpoint.h" />
    <ClInclude Include="elgamal.h" />
    <ClInclude Include="emsa2.h" />
    <ClInclude Include="eprecomp.h" />
    <ClInclude Include="esign.h" />
    <ClInclude Include="factory.h" />
    <ClInclude Include="fhmqv.h" />
    <ClInclude Include="files.h" />
    <ClInclude Include="filters.h" />
    <ClInclude Include="fips140.h" />
    <ClInclude Include="fltrimpl.h" />
    <ClInclude Include="gcm.h" />
    <ClInclude Include="gf256.h" />
    <ClInclude Include="gf2n.h" />
    <ClInclude Include="gf2_32.h" />
    <ClInclude Include="gfpcrypt.h" />
    <ClInclude Include="gost.h" />
    <ClInclude Include="gzip.h" />
    <ClInclude Include="hashfwd.h" />
    <ClInclude Include="hex.h" />
    <ClInclude Include="hkdf.h" />
    <ClInclude Include="hmac.h" />
    <ClInclude Include="hmqv.h" />
    <ClInclude Include="hrtimer.h" />
    <ClInclude Include="ida.h" />
    <ClInclude Include="idea.h" />
    <ClInclude Include="integer.h" />
    <ClInclude Include="iterhash.h" />
    <ClInclude Include="kalyna.h" />
    <ClInclude Include="keccak.h" />
    <ClInclude Include="lubyrack.h" />
    <ClInclude Include="luc.h" />
    <ClInclude Include="mars.h" />
    <ClInclude Include="md2.h" />
    <ClInclude Include="md4.h" />
    <ClInclude Include="md5.h" />
    <ClInclude Include="mdc.h" />
    <ClInclude Include="mersenne.h" />
    <ClInclude Include="misc.h" />
    <ClInclude Include="modarith.h" />
    <ClInclude Include="modes.h" />
    <ClInclude Include="modexppc.h" />
    <ClInclude Include="mqueue.h" />
    <ClInclude Include="mqv.h" />
    <ClInclude Include="naclite.h" />
    <ClInclude Include="nbtheory.h" />
    <ClInclude Include="network.h" />
    <ClInclude Include="nr.h" />
    <ClInclude Include="oaep.h" />
    <ClInclude Include="oids.h" />
    <ClInclude Include="osrng.h" />
    <ClInclude Include="ossig.h" />
    <ClInclude Include="padlkrng.h" />
    <ClInclude Include="panama.h" />
    <ClInclude Include="pch.h" />
    <ClInclude Include="pkcspad.h" />
    <ClInclude Include="poly1305.h" />
    <ClInclude Include="polynomi.h" />
    <ClInclude Include="ppc-simd.h" />
    <ClInclude Include="pssr.h" />
    <ClInclude Include="pubkey.h" />
    <ClInclude Include="pwdbased.h" />
    <ClInclude Include="queue.h" />
    <ClInclude Include="rabin.h" />
    <ClInclude Include="randpool.h" />
    <ClInclude Include="rc2.h" />
    <ClInclude Include="rc5.h" />
    <ClInclude Include="rc6.h" />
    <ClInclude Include="rdrand.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="rijndael.h" />
    <ClInclude Include="ripemd.h" />
    <ClInclude Include="rng.h" />
    <ClInclude Include="rsa.h" />
    <ClInclude Include="rw.h" />
    <ClInclude Include="safer.h" />
    <ClInclude Include="salsa.h" />
    <ClInclude Include="scrypt.h" />
    <ClInclude Include="seal.h" />
    <ClInclude Include="secblock.h" />
    <ClInclude Include="seckey.h" />
    <ClInclude Include="seed.h" />
    <ClInclude Include="serpent.h" />
    <ClInclude Include="serpentp.h" />
    <ClInclude Include="sha.h" />
    <ClInclude Include="sha3.h" />
    <ClInclude Include="shacal2.h" />
    <ClInclude Include="shark.h" />
    <ClInclude Include="simon.h" />
    <ClInclude Include="simple.h" />
    <ClInclude Include="siphash.h" />
    <ClInclude Include="skipjack.h" />
    <ClInclude Include="sm3.h" />
    <ClInclude Include="sm4.h" />
    <ClInclude Include="smartptr.h" />
    <ClInclude Include="socketft.h" />
    <ClInclude Include="sosemanuk.h" />
    <ClInclude Include="speck.h" />
    <ClInclude Include="square.h" />
    <ClInclude Include="stdcpp.h" />
    <ClInclude Include="strciphr.h" />
    <ClInclude Include="tea.h" />
    <ClInclude Include="threefish.h" />
    <ClInclude Include="tiger.h" />
    <ClInclude Include="trap.h" />
    <ClInclude Include="trdlocal.h" />
    <ClInclude Include="trunhash.h" />
    <ClInclude Include="ttmac.h" />
    <ClInclude Include="tweetnacl.h" />
    <ClInclude Include="twofish.h" />
    <ClInclude Include="validate.h" />
    <ClInclude Include="vmac.h" />
    <ClInclude Include="wait.h" />
    <ClInclude Include="wake.h" />
    <ClInclude Include="whrlpool.h" />
    <ClInclude Include="winpipes.h" />
    <ClInclude Include="words.h" />
    <ClInclude Include="xtr.h" />
    <ClInclude Include="xtrcrypt.h" />
    <ClInclude Include="zdeflate.h" />
    <ClInclude Include="zinflate.h" />
    <ClInclude Include="zlib.h" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="cryptopp.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="rdrand.asm" />
    <None Include="x64dll.asm" />
    <None Include="x64masm.asm" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>