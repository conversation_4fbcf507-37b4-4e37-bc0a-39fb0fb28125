# 本机代理配置指南

根据您的网络配置，以下是几种常见的本机代理设置方法。

## 您的网络信息

检测到的本机 IP 地址：
- `**************` (主要网络接口)
- `**************` (可能是 VPN 或虚拟网络)
- `**********` (可能是代理软件创建的接口)

## 常见代理软件配置

### 1. Clash for Windows/ClashX (推荐)

如果您使用 Clash 系列代理软件：

```bash
# 通常 Clash 监听在 7890 端口
./build-with-proxy.sh \
  --http-proxy http://**************:7890 \
  --https-proxy http://**************:7890
```

**Clash 配置检查**：
1. 打开 Clash 控制面板
2. 确认 "Allow LAN" 选项已开启
3. 查看 HTTP 端口（通常是 7890）
4. 查看 SOCKS 端口（通常是 7891）

### 2. V2Ray/V2RayN

如果您使用 V2Ray：

```bash
# V2Ray 默认 HTTP 代理端口通常是 10809
./build-with-proxy.sh \
  --http-proxy http://**************:10809 \
  --https-proxy http://**************:10809

# 或者使用 SOCKS5 代理（端口通常是 10808）
./build-with-proxy.sh \
  --http-proxy socks5://**************:10808 \
  --https-proxy socks5://**************:10808
```

### 3. Shadowsocks

如果您使用 Shadowsocks：

```bash
# Shadowsocks 本地代理端口通常是 1080
./build-with-proxy.sh \
  --http-proxy socks5://**************:1080 \
  --https-proxy socks5://**************:1080
```

### 4. Surge

如果您使用 Surge：

```bash
# Surge HTTP 代理端口通常是 6152
./build-with-proxy.sh \
  --http-proxy http://**************:6152 \
  --https-proxy http://**************:6152
```

### 5. Proxifier

如果您使用 Proxifier：

```bash
# Proxifier 通常配置为系统代理，需要查看具体端口
./build-with-proxy.sh \
  --http-proxy http://**************:8080 \
  --https-proxy http://**************:8080
```

## 如何确定您的代理端口

### 方法 1: 检查系统代理设置

**macOS**：
```bash
# 查看系统代理设置
networksetup -getwebproxy "Wi-Fi"
networksetup -getsecurewebproxy "Wi-Fi"
```

**Linux**：
```bash
# 查看环境变量
echo $HTTP_PROXY
echo $HTTPS_PROXY
```

### 方法 2: 检查监听端口

```bash
# 查看本机监听的端口
netstat -an | grep LISTEN | grep -E ":(7890|10809|1080|6152|8080)"
```

### 方法 3: 检查代理软件界面

大多数代理软件都会在界面中显示本地监听端口。

## Docker 网络配置

### 使用 host.docker.internal (推荐)

在较新版本的 Docker 中，可以使用特殊域名：

```bash
./build-with-proxy.sh \
  --http-proxy http://host.docker.internal:7890 \
  --https-proxy http://host.docker.internal:7890
```

### 使用 Docker 网桥 IP

```bash
# 查看 Docker 网桥 IP
docker network inspect bridge | grep Gateway

# 通常是 **********，但需要确保代理软件监听所有接口
./build-with-proxy.sh \
  --http-proxy http://**********:7890 \
  --https-proxy http://**********:7890
```

## 测试代理连接

创建一个测试脚本来验证代理是否工作：

```bash
# 测试代理连接
curl -x http://**************:7890 -I https://www.google.com
```

如果返回 200 状态码，说明代理配置正确。

## 常见问题解决

### 1. 连接被拒绝

**原因**: 代理软件没有允许 LAN 连接

**解决方案**:
- 在代理软件中开启 "Allow LAN" 或类似选项
- 确保防火墙允许相应端口

### 2. 认证失败

**原因**: 代理需要用户名密码

**解决方案**:
```bash
./build-with-proxy.sh \
  --http-proxy ******************************************** \
  --https-proxy ********************************************
```

### 3. DNS 解析问题

**解决方案**:
```bash
./build-with-proxy.sh \
  --http-proxy http://**************:7890 \
  --https-proxy http://**************:7890 \
  --no-proxy localhost,127.0.0.1,.local,************/24
```

## 推荐配置

基于您的网络环境，推荐尝试以下配置（按优先级排序）：

### 配置 1: Clash (最常见)
```bash
./build-with-proxy.sh \
  --http-proxy http://host.docker.internal:7890 \
  --https-proxy http://host.docker.internal:7890
```

### 配置 2: V2Ray
```bash
./build-with-proxy.sh \
  --http-proxy http://host.docker.internal:10809 \
  --https-proxy http://host.docker.internal:10809
```

### 配置 3: 使用本机 IP
```bash
./build-with-proxy.sh \
  --http-proxy http://**************:7890 \
  --https-proxy http://**************:7890
```

## 验证步骤

1. **确认代理软件运行**: 检查代理软件是否正常运行
2. **确认端口监听**: 使用 `netstat` 确认端口正在监听
3. **测试连接**: 使用 `curl` 测试代理连接
4. **开始构建**: 使用正确的代理配置开始构建

如果仍有问题，请告诉我您使用的具体代理软件，我可以提供更精确的配置。
