#!/bin/bash

# OnlyOffice X2T WASM 代理构建脚本
# 使用此脚本可以通过代理服务器构建 Docker 镜像

set -e

# 默认代理配置（可以通过环境变量覆盖）
DEFAULT_HTTP_PROXY="${HTTP_PROXY:-}"
DEFAULT_HTTPS_PROXY="${HTTPS_PROXY:-}"
DEFAULT_NO_PROXY="${NO_PROXY:-localhost,127.0.0.1,.local}"

# 显示使用说明
show_usage() {
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  --http-proxy URL        设置 HTTP 代理 (例如: http://proxy.company.com:8080)"
    echo "  --https-proxy URL       设置 HTTPS 代理 (例如: http://proxy.company.com:8080)"
    echo "  --no-proxy HOSTS        设置不使用代理的主机列表 (例如: localhost,127.0.0.1,.local)"
    echo "  --no-cache              不使用 Docker 构建缓存"
    echo "  --target TARGET         指定构建目标 (默认: output)"
    echo ""
    echo "环境变量:"
    echo "  HTTP_PROXY              HTTP 代理 URL"
    echo "  HTTPS_PROXY             HTTPS 代理 URL"
    echo "  NO_PROXY                不使用代理的主机列表"
    echo ""
    echo "示例:"
    echo "  $0 --http-proxy http://proxy.company.com:8080 --https-proxy http://proxy.company.com:8080"
    echo "  HTTP_PROXY=http://proxy.company.com:8080 HTTPS_PROXY=http://proxy.company.com:8080 $0"
    echo "  $0 --target test  # 构建并运行测试"
}

# 解析命令行参数
HTTP_PROXY_ARG="$DEFAULT_HTTP_PROXY"
HTTPS_PROXY_ARG="$DEFAULT_HTTPS_PROXY"
NO_PROXY_ARG="$DEFAULT_NO_PROXY"
USE_CACHE="--cache"
BUILD_TARGET="output"

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        --http-proxy)
            HTTP_PROXY_ARG="$2"
            shift 2
            ;;
        --https-proxy)
            HTTPS_PROXY_ARG="$2"
            shift 2
            ;;
        --no-proxy)
            NO_PROXY_ARG="$2"
            shift 2
            ;;
        --no-cache)
            USE_CACHE="--no-cache"
            shift
            ;;
        --target)
            BUILD_TARGET="$2"
            shift 2
            ;;
        *)
            echo "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
done

# 显示配置信息
echo "=== OnlyOffice X2T WASM 构建配置 ==="
echo "HTTP 代理:  ${HTTP_PROXY_ARG:-未设置}"
echo "HTTPS 代理: ${HTTPS_PROXY_ARG:-未设置}"
echo "NO_PROXY:   ${NO_PROXY_ARG:-未设置}"
echo "构建目标:   $BUILD_TARGET"
echo "使用缓存:   $([ "$USE_CACHE" = "--cache" ] && echo "是" || echo "否")"
echo "=================================="

# 构建 Docker 构建参数
BUILD_ARGS=""
if [ -n "$HTTP_PROXY_ARG" ]; then
    BUILD_ARGS="$BUILD_ARGS --build-arg HTTP_PROXY=$HTTP_PROXY_ARG"
    BUILD_ARGS="$BUILD_ARGS --build-arg http_proxy=$HTTP_PROXY_ARG"
fi

if [ -n "$HTTPS_PROXY_ARG" ]; then
    BUILD_ARGS="$BUILD_ARGS --build-arg HTTPS_PROXY=$HTTPS_PROXY_ARG"
    BUILD_ARGS="$BUILD_ARGS --build-arg https_proxy=$HTTPS_PROXY_ARG"
fi

if [ -n "$NO_PROXY_ARG" ]; then
    BUILD_ARGS="$BUILD_ARGS --build-arg NO_PROXY=$NO_PROXY_ARG"
    BUILD_ARGS="$BUILD_ARGS --build-arg no_proxy=$NO_PROXY_ARG"
fi

# 执行构建
echo "开始构建..."
echo "执行命令: docker build $USE_CACHE $BUILD_ARGS --target $BUILD_TARGET -t onlyoffice-x2t-wasm:$BUILD_TARGET ."

docker build $USE_CACHE $BUILD_ARGS --target $BUILD_TARGET -t onlyoffice-x2t-wasm:$BUILD_TARGET .

echo ""
echo "构建完成！"

# 根据构建目标提供后续操作建议
case $BUILD_TARGET in
    "output")
        echo ""
        echo "要提取构建产物，请运行:"
        echo "  docker create --name temp-container onlyoffice-x2t-wasm:output"
        echo "  docker cp temp-container:/ ./output/"
        echo "  docker rm temp-container"
        ;;
    "test")
        echo ""
        echo "要查看测试结果，请运行:"
        echo "  docker create --name temp-container onlyoffice-x2t-wasm:test"
        echo "  docker cp temp-container:/ ./test-results/"
        echo "  docker rm temp-container"
        ;;
    "test-output")
        echo ""
        echo "要提取测试输出，请运行:"
        echo "  docker create --name temp-container onlyoffice-x2t-wasm:test-output"
        echo "  docker cp temp-container:/ ./test-output/"
        echo "  docker rm temp-container"
        ;;
esac
