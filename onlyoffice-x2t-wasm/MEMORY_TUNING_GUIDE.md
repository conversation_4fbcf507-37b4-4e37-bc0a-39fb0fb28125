# WASM 内存参数调优指南

## 当前配置

```bash
-sINITIAL_MEMORY=134217728    # 128MB 初始内存
-sMAXIMUM_MEMORY=2147483648   # 2GB 最大内存
-sSTACK_SIZE=8388608          # 8MB 栈大小
-sALLOW_MEMORY_GROWTH         # 允许内存增长
-sASSERTIONS=0               # 禁用断言（性能优化）
```

## 参数说明

### INITIAL_MEMORY (初始内存)
- **当前值**: 134217728 (128MB)
- **建议范围**: 64MB - 256MB
- **调整原则**:
  - 小文档: 64MB (67108864)
  - 中等文档: 128MB (134217728) ✅ 当前设置
  - 大文档: 256MB (268435456)

### MAXIMUM_MEMORY (最大内存)
- **当前值**: 2147483648 (2GB)
- **建议范围**: 512MB - 2GB
- **调整原则**:
  - 移动设备: 512MB (536870912)
  - 桌面浏览器: 1GB (1073741824)
  - 服务器环境: 2GB (2147483648) ✅ 当前设置

### STACK_SIZE (栈大小)
- **当前值**: 8388608 (8MB)
- **建议范围**: 4MB - 16MB
- **调整原则**:
  - 简单文档: 4MB (4194304)
  - 复杂文档: 8MB (8388608) ✅ 当前设置
  - 极复杂文档: 16MB (16777216)

## 针对不同场景的配置建议

### 1. 移动端优化配置
```bash
-sINITIAL_MEMORY=67108864     # 64MB
-sMAXIMUM_MEMORY=536870912    # 512MB
-sSTACK_SIZE=4194304          # 4MB
```

### 2. 桌面端平衡配置
```bash
-sINITIAL_MEMORY=134217728    # 128MB ✅ 当前
-sMAXIMUM_MEMORY=1073741824   # 1GB
-sSTACK_SIZE=8388608          # 8MB ✅ 当前
```

### 3. 服务器端高性能配置
```bash
-sINITIAL_MEMORY=268435456    # 256MB
-sMAXIMUM_MEMORY=2147483648   # 2GB ✅ 当前
-sSTACK_SIZE=16777216         # 16MB
```

### 4. 大文档处理配置
```bash
-sINITIAL_MEMORY=268435456    # 256MB
-sMAXIMUM_MEMORY=2147483648   # 2GB ✅ 当前
-sSTACK_SIZE=16777216         # 16MB
-sALLOW_MEMORY_GROWTH         # ✅ 当前
```

## 如何修改配置

### 方法 1: 修改 Dockerfile
编辑 `Dockerfile` 第 837-840 行：

```dockerfile
-l "-sINITIAL_MEMORY=新值" \
-l "-sMAXIMUM_MEMORY=新值" \
-l "-sSTACK_SIZE=新值" \
```

### 方法 2: 使用环境变量
在构建脚本中添加：

```bash
export EMCC_INITIAL_MEMORY=134217728
export EMCC_MAXIMUM_MEMORY=2147483648
export EMCC_STACK_SIZE=8388608
```

## 性能调优参数

### 额外的优化参数
```bash
-sASSERTIONS=0                # ✅ 已添加 - 禁用断言
-sNO_EXIT_RUNTIME=1           # 不退出运行时
-sENVIRONMENT=web             # 仅 Web 环境
-sMODULARIZE=1               # 模块化输出
-sEXPORT_ES6=1               # ES6 模块导出
-sUSE_ES6_IMPORT_META=0      # 兼容性
-sOPTIMIZE_SIZE=0            # 优化性能而非大小
```

### 调试参数（开发时使用）
```bash
-sASSERTIONS=1               # 启用断言
-sSAFE_HEAP=1                # 堆安全检查
-sSTACK_OVERFLOW_CHECK=1     # 栈溢出检查
-g                           # ✅ 已添加 - 调试信息
```

## 内存问题诊断

### 常见错误及解决方案

1. **"memory access out of bounds"**
   - 增加 INITIAL_MEMORY
   - 确保 ALLOW_MEMORY_GROWTH 启用

2. **"Cannot enlarge memory arrays"**
   - 增加 MAXIMUM_MEMORY
   - 检查浏览器内存限制

3. **栈溢出错误**
   - 增加 STACK_SIZE
   - 优化递归算法

4. **加载缓慢**
   - 减少 INITIAL_MEMORY
   - 启用 ALLOW_MEMORY_GROWTH

## 测试不同配置

### 快速测试脚本
```bash
# 测试当前配置
./build-with-proxy.sh --target output

# 测试移动端配置
# 修改 Dockerfile 后重新构建

# 测试服务器端配置
# 修改 Dockerfile 后重新构建
```

### 内存使用监控
在浏览器中使用：
```javascript
// 检查内存使用
console.log('WASM Memory:', Module.wasmMemory.buffer.byteLength / 1024 / 1024, 'MB');

// 监控内存增长
setInterval(() => {
    if (Module.wasmMemory) {
        const mb = Module.wasmMemory.buffer.byteLength / 1024 / 1024;
        console.log('Current memory:', mb.toFixed(2), 'MB');
    }
}, 1000);
```

## 推荐的调优流程

1. **从当前配置开始测试**
2. **如果内存不足，逐步增加 INITIAL_MEMORY**
3. **如果仍有问题，增加 MAXIMUM_MEMORY**
4. **如果出现栈溢出，增加 STACK_SIZE**
5. **在生产环境中禁用调试参数**

当前的配置已经是一个很好的平衡点，适合大多数使用场景。
