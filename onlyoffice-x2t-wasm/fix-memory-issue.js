#!/usr/bin/env node

// 修复 WASM 内存问题的脚本
const fs = require('fs');
const path = require('path');

console.log('🔧 修复 WASM 内存配置...');

const jsPath = '/Users/<USER>/Desktop/office/output/x2t.js';
const backupPath = jsPath + '.backup';

// 备份原文件
if (!fs.existsSync(backupPath)) {
    fs.copyFileSync(jsPath, backupPath);
    console.log('✅ 已备份原文件:', backupPath);
}

// 读取文件内容
let content = fs.readFileSync(jsPath, 'utf8');

console.log('🔍 分析当前配置...');

// 查找当前的内存配置
const memoryMatches = content.match(/var\s+maxHeapSize\s*=\s*(\d+)/);
if (memoryMatches) {
    console.log('📊 当前最大堆大小:', parseInt(memoryMatches[1]).toLocaleString(), 'bytes');
} else {
    console.log('⚠️  未找到最大堆大小配置');
}

// 修复方案 1: 增加初始内存和最大内存
console.log('\n🛠️  应用内存修复...');

// 查找并替换内存相关的配置
let modified = false;

// 1. 增加最大堆大小限制
if (content.includes('2147483648')) {
    console.log('✅ 已找到 2GB 内存限制');
} else {
    // 查找并替换较小的内存限制
    content = content.replace(
        /(var\s+maxHeapSize\s*=\s*)(\d+)/g,
        (match, prefix, size) => {
            const currentSize = parseInt(size);
            if (currentSize < 2147483648) {
                console.log(`📈 增加最大堆大小: ${currentSize.toLocaleString()} -> 2,147,483,648 bytes (2GB)`);
                modified = true;
                return prefix + '2147483648';
            }
            return match;
        }
    );
}

// 2. 修改内存增长策略
const memoryGrowthPattern = /overGrownHeapSize\s*=\s*Math\.min\(overGrownHeapSize,\s*requestedSize\s*\+\s*(\d+)\s*\)/;
const memoryGrowthMatch = content.match(memoryGrowthPattern);
if (memoryGrowthMatch) {
    const currentGrowth = parseInt(memoryGrowthMatch[1]);
    if (currentGrowth < 268435456) { // 256MB
        content = content.replace(
            memoryGrowthPattern,
            'overGrownHeapSize = Math.min(overGrownHeapSize, requestedSize + 268435456 )'
        );
        console.log(`📈 增加内存增长缓冲: ${currentGrowth.toLocaleString()} -> 268,435,456 bytes (256MB)`);
        modified = true;
    }
}

// 3. 添加更好的错误处理
const errorHandlingCode = `
// Enhanced memory error handling
var originalGrowMemory = growMemory;
growMemory = function(size) {
    try {
        console.log('🔄 尝试增长内存到:', size.toLocaleString(), 'bytes');
        var result = originalGrowMemory(size);
        if (result) {
            console.log('✅ 内存增长成功');
        }
        return result;
    } catch (e) {
        console.error('❌ 内存增长失败:', e.message);
        console.log('💡 建议: 尝试处理较小的文档或增加系统内存');
        throw e;
    }
};
`;

// 在适当位置插入错误处理代码
if (!content.includes('Enhanced memory error handling')) {
    const insertPoint = content.indexOf('var growMemory = (size) => {');
    if (insertPoint !== -1) {
        content = content.slice(0, insertPoint) + errorHandlingCode + content.slice(insertPoint);
        console.log('✅ 添加增强的内存错误处理');
        modified = true;
    }
}

// 4. 添加内存监控
const monitoringCode = `
// Memory monitoring
if (typeof window !== 'undefined') {
    window.checkWasmMemory = function() {
        if (wasmMemory && wasmMemory.buffer) {
            const used = wasmMemory.buffer.byteLength;
            const usedMB = (used / 1024 / 1024).toFixed(2);
            console.log('📊 当前 WASM 内存使用:', usedMB, 'MB');
            return { bytes: used, mb: usedMB };
        }
        return null;
    };
}
`;

if (!content.includes('Memory monitoring')) {
    const insertPoint = content.indexOf('updateMemoryViews();');
    if (insertPoint !== -1) {
        const lineEnd = content.indexOf('\n', insertPoint);
        content = content.slice(0, lineEnd + 1) + monitoringCode + content.slice(lineEnd + 1);
        console.log('✅ 添加内存监控功能');
        modified = true;
    }
}

// 保存修改后的文件
if (modified) {
    fs.writeFileSync(jsPath, content);
    console.log('✅ 已保存修复后的文件');
} else {
    console.log('ℹ️  文件已经包含所需的修复');
}

// 创建一个测试用的 HTML 文件
const testHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>WASM 内存测试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>OnlyOffice X2T WASM 内存测试</h1>
    <div id="status">正在加载...</div>
    <div id="memory-info"></div>
    <button onclick="testMemory()">测试内存</button>
    <button onclick="testConversion()">测试转换</button>
    
    <script src="x2t.js"></script>
    <script>
        let moduleReady = false;
        
        Module = {
            onRuntimeInitialized: function() {
                moduleReady = true;
                document.getElementById('status').innerHTML = '✅ WASM 模块已加载';
                updateMemoryInfo();
            },
            print: function(text) {
                console.log('WASM:', text);
            },
            printErr: function(text) {
                console.error('WASM Error:', text);
            }
        };
        
        function updateMemoryInfo() {
            if (typeof checkWasmMemory === 'function') {
                const info = checkWasmMemory();
                if (info) {
                    document.getElementById('memory-info').innerHTML = 
                        '📊 内存使用: ' + info.mb + ' MB (' + info.bytes.toLocaleString() + ' bytes)';
                }
            }
        }
        
        function testMemory() {
            if (!moduleReady) {
                alert('WASM 模块尚未加载完成');
                return;
            }
            
            updateMemoryInfo();
            
            // 尝试分配一些内存来测试
            try {
                const testSize = 1024 * 1024; // 1MB
                const ptr = Module._malloc(testSize);
                if (ptr) {
                    console.log('✅ 成功分配', testSize, '字节内存');
                    Module._free(ptr);
                    console.log('✅ 内存已释放');
                } else {
                    console.error('❌ 内存分配失败');
                }
            } catch (e) {
                console.error('❌ 内存测试失败:', e);
            }
            
            updateMemoryInfo();
        }
        
        function testConversion() {
            if (!moduleReady) {
                alert('WASM 模块尚未加载完成');
                return;
            }
            
            // 这里可以添加实际的文档转换测试
            console.log('🧪 开始转换测试...');
            alert('转换测试功能需要根据具体的 API 实现');
        }
        
        // 定期更新内存信息
        setInterval(updateMemoryInfo, 5000);
    </script>
</body>
</html>
`;

const testHtmlPath = '/Users/<USER>/Desktop/office/output/memory-test.html';
fs.writeFileSync(testHtmlPath, testHtml);
console.log('✅ 创建测试页面:', testHtmlPath);

console.log('\n🎯 修复完成！');
console.log('\n📋 修复内容:');
console.log('1. ✅ 增加最大内存限制到 2GB');
console.log('2. ✅ 优化内存增长策略');
console.log('3. ✅ 添加增强的错误处理');
console.log('4. ✅ 添加内存监控功能');
console.log('5. ✅ 创建测试页面');

console.log('\n🚀 测试方法:');
console.log('1. 在浏览器中打开:', testHtmlPath);
console.log('2. 检查控制台输出');
console.log('3. 使用测试按钮验证功能');

console.log('\n💡 如果问题仍然存在:');
console.log('1. 重新构建 WASM 模块以应用编译时的内存配置');
console.log('2. 检查输入文档的大小和复杂度');
console.log('3. 考虑在服务器端处理大文档');
