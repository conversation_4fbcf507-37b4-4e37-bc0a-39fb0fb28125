# 基础镜像 - 包含所有系统依赖和工具
FROM ubuntu:22.04 AS base

SHELL ["/bin/bash", "-c"]

# 代理配置 - 可以通过构建参数传入
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG NO_PROXY
ARG http_proxy
ARG https_proxy
ARG no_proxy

# 设置环境变量
ENV HTTP_PROXY=${HTTP_PROXY}
ENV HTTPS_PROXY=${HTTPS_PROXY}
ENV NO_PROXY=${NO_PROXY}
ENV http_proxy=${http_proxy}
ENV https_proxy=${https_proxy}
ENV no_proxy=${no_proxy}

# 配置 apt 代理（如果设置了代理）
RUN if [ -n "$HTTP_PROXY" ] || [ -n "$http_proxy" ]; then \
        echo "Acquire::http::Proxy \"${HTTP_PROXY:-$http_proxy}\";" > /etc/apt/apt.conf.d/01proxy && \
        echo "Acquire::https::Proxy \"${HTTPS_PROXY:-$https_proxy}\";" >> /etc/apt/apt.conf.d/01proxy; \
    fi

# 安装系统依赖
RUN --mount=target=/var/lib/apt/lists,type=cache,sharing=locked \
    --mount=target=/var/cache/apt,type=cache,sharing=locked \
    apt update \
    && apt install -y \
       git \
       python-is-python3 \
       xz-utils \
       lbzip2 \
       automake \
       libtool \
       autoconf \
       make \
       qt6-base-dev \
       build-essential \
       cmake \
       zip \
       pkg-config \
       wget \
       curl \
       ca-certificates \
    && apt clean \
    && rm -rf /var/lib/apt/lists/*

# 配置 git 代理（在安装 git 之后）
RUN if [ -n "$HTTP_PROXY" ] || [ -n "$http_proxy" ]; then \
        git config --global http.proxy "${HTTP_PROXY:-$http_proxy}" && \
        git config --global https.proxy "${HTTPS_PROXY:-$https_proxy}"; \
    fi

# 安装和配置 Emscripten
WORKDIR /
RUN git clone https://github.com/emscripten-core/emsdk.git
WORKDIR /emsdk
ARG emversion=4.0.11
RUN git fetch -a && git checkout $emversion
RUN ./emsdk install $emversion
RUN ./emsdk activate $emversion

# 配置 Qt
RUN . /emsdk/emsdk_env.sh && qtchooser -install qt6 $(which qmake6)
ENV QT_SELECT=qt6

# 复制构建脚本
WORKDIR /
COPY embuild.sh /bin/embuild.sh
RUN chmod +x /bin/embuild.sh

# 设置工作目录
WORKDIR /core

# 标记版本
LABEL version="1.0"
LABEL description="OnlyOffice X2T WASM Base Image with Emscripten and dependencies"
