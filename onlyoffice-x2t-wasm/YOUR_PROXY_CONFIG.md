# 您的代理配置

根据检测到的系统配置，为您提供最适合的代理设置。

## 检测到的配置

- **代理服务器**: 127.0.0.1:7890 (HTTP/HTTPS)
- **状态**: ✅ 正在运行并监听所有接口
- **本机 IP**: **************

## 推荐配置（按优先级排序）

### 配置 1: 使用 host.docker.internal (最推荐)

```bash
./build-with-proxy.sh \
  --http-proxy http://host.docker.internal:7890 \
  --https-proxy http://host.docker.internal:7890
```

这是最稳定的方法，Docker 会自动解析到宿主机。

### 配置 2: 使用本机 IP

```bash
./build-with-proxy.sh \
  --http-proxy http://**************:7890 \
  --https-proxy http://**************:7890
```

### 配置 3: 使用 Docker 网桥 IP

```bash
./build-with-proxy.sh \
  --http-proxy http://**********:7890 \
  --https-proxy http://**********:7890
```

## 快速测试

在构建之前，先测试代理是否工作：

```bash
# 测试方法 1: 使用 host.docker.internal
docker run --rm curlimages/curl:latest \
  curl -x http://host.docker.internal:7890 -I https://www.google.com

# 测试方法 2: 使用本机 IP
docker run --rm curlimages/curl:latest \
  curl -x http://**************:7890 -I https://www.google.com
```

如果返回 `HTTP/2 200` 或类似成功响应，说明代理配置正确。

## 完整构建命令

### 标准构建
```bash
./build-with-proxy.sh \
  --http-proxy http://host.docker.internal:7890 \
  --https-proxy http://host.docker.internal:7890 \
  --target output
```

### 包含测试的构建
```bash
./build-with-proxy.sh \
  --http-proxy http://host.docker.internal:7890 \
  --https-proxy http://host.docker.internal:7890 \
  --target test
```

### 无缓存构建（如果遇到问题）
```bash
./build-with-proxy.sh \
  --http-proxy http://host.docker.internal:7890 \
  --https-proxy http://host.docker.internal:7890 \
  --no-cache \
  --target output
```

## 故障排除

### 如果遇到连接问题

1. **检查代理软件设置**:
   - 确保 "Allow LAN" 或 "允许局域网连接" 已开启
   - 确认代理软件正在运行

2. **检查防火墙**:
   ```bash
   # macOS 检查防火墙状态
   sudo /usr/libexec/ApplicationFirewall/socketfilterfw --getglobalstate
   ```

3. **使用备用配置**:
   如果 `host.docker.internal` 不工作，尝试使用本机 IP：
   ```bash
   ./build-with-proxy.sh \
     --http-proxy http://**************:7890 \
     --https-proxy http://**************:7890
   ```

### 如果代理需要认证

如果您的代理需要用户名和密码：
```bash
./build-with-proxy.sh \
  --http-proxy http://username:<EMAIL>:7890 \
  --https-proxy http://username:<EMAIL>:7890
```

## 验证步骤

1. **运行代理测试**:
   ```bash
   docker run --rm curlimages/curl:latest \
     curl -x http://host.docker.internal:7890 -I https://www.google.com
   ```

2. **如果测试成功，开始构建**:
   ```bash
   ./build-with-proxy.sh \
     --http-proxy http://host.docker.internal:7890 \
     --https-proxy http://host.docker.internal:7890
   ```

3. **监控构建过程**:
   构建过程中会显示网络请求，确认它们通过代理成功。

## 常见代理软件对应关系

根据您的配置（端口 7890），您可能使用的是：
- **Clash for Windows/ClashX** ✅ (最可能)
- **V2RayU/V2RayN** (如果配置了 HTTP 端口为 7890)
- **Surge** (如果自定义了端口)

## 下一步

1. 首先运行代理测试命令
2. 如果测试成功，使用推荐的配置 1 开始构建
3. 如果遇到问题，尝试配置 2 或查看故障排除部分

祝您构建顺利！🚀
