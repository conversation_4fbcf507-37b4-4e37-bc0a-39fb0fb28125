# OnlyOffice X2T WASM 代理配置指南

本文档说明如何在企业网络环境中通过代理服务器构建 OnlyOffice X2T WASM 项目。

## 概述

该 Dockerfile 已经配置了完整的代理支持，包括：
- APT 包管理器代理配置
- Git 代理配置
- Wget 代理配置
- 环境变量传递

## 快速开始

### 方法 1: 使用构建脚本（推荐）

```bash
# 给脚本执行权限
chmod +x build-with-proxy.sh

# 使用代理构建
./build-with-proxy.sh --http-proxy http://proxy.company.com:8080 --https-proxy http://proxy.company.com:8080
```

### 方法 2: 使用环境变量

```bash
# 设置代理环境变量
export HTTP_PROXY=http://proxy.company.com:8080
export HTTPS_PROXY=http://proxy.company.com:8080
export NO_PROXY=localhost,127.0.0.1,.local

# 构建
./build-with-proxy.sh
```

### 方法 3: 直接使用 Docker 命令

```bash
docker build \
  --build-arg HTTP_PROXY=http://proxy.company.com:8080 \
  --build-arg HTTPS_PROXY=http://proxy.company.com:8080 \
  --build-arg NO_PROXY=localhost,127.0.0.1,.local \
  --target output \
  -t onlyoffice-x2t-wasm:latest .
```

## 代理配置参数

### 必需参数

- `HTTP_PROXY` / `http_proxy`: HTTP 代理服务器 URL
- `HTTPS_PROXY` / `https_proxy`: HTTPS 代理服务器 URL

### 可选参数

- `NO_PROXY` / `no_proxy`: 不使用代理的主机列表，用逗号分隔

### 代理 URL 格式

```
http://[username:password@]proxy.server.com:port
```

示例：
- `http://proxy.company.com:8080`
- `http://user:<EMAIL>:8080`
- `socks5://proxy.company.com:1080`

## 常见代理配置场景

### 1. 企业 HTTP/HTTPS 代理

```bash
./build-with-proxy.sh \
  --http-proxy http://proxy.company.com:8080 \
  --https-proxy http://proxy.company.com:8080 \
  --no-proxy localhost,127.0.0.1,.local,.company.com
```

### 2. 需要认证的代理

```bash
./build-with-proxy.sh \
  --http-proxy http://username:<EMAIL>:8080 \
  --https-proxy http://username:<EMAIL>:8080
```

### 3. SOCKS5 代理

```bash
./build-with-proxy.sh \
  --http-proxy socks5://proxy.company.com:1080 \
  --https-proxy socks5://proxy.company.com:1080
```

## 构建目标

### 1. 默认构建（output）

```bash
./build-with-proxy.sh --target output
```

构建完成后提取产物：
```bash
docker create --name temp-container onlyoffice-x2t-wasm:output
docker cp temp-container:/ ./output/
docker rm temp-container
```

### 2. 运行测试

```bash
./build-with-proxy.sh --target test
```

### 3. 仅提取测试结果

```bash
./build-with-proxy.sh --target test-output
```

## 故障排除

### 1. 代理连接失败

**症状**: 构建过程中出现网络连接错误

**解决方案**:
- 检查代理服务器地址和端口是否正确
- 确认代理服务器是否需要认证
- 检查防火墙设置

### 2. SSL 证书问题

**症状**: HTTPS 连接出现证书验证错误

**解决方案**:
```bash
# 在构建参数中添加
--build-arg ADDITIONAL_ARGS="--build-arg SSL_VERIFY=false"
```

### 3. DNS 解析问题

**症状**: 无法解析域名

**解决方案**:
- 确保 DNS 服务器配置正确
- 在 NO_PROXY 中添加内网域名

### 4. 构建缓存问题

**症状**: 代理配置更改后仍使用旧配置

**解决方案**:
```bash
./build-with-proxy.sh --no-cache
```

## 高级配置

### 1. 自定义 Docker 构建参数

编辑 `build-with-proxy.sh` 脚本，在 `BUILD_ARGS` 中添加额外参数：

```bash
BUILD_ARGS="$BUILD_ARGS --build-arg CUSTOM_ARG=value"
```

### 2. 多阶段构建优化

如果只需要特定阶段，可以指定目标：

```bash
./build-with-proxy.sh --target base  # 只构建基础镜像
./build-with-proxy.sh --target build # 构建到 build 阶段
```

### 3. 并行构建

```bash
docker build --parallel --build-arg HTTP_PROXY=... ...
```

## 环境变量参考

| 变量名 | 描述 | 示例 |
|--------|------|------|
| HTTP_PROXY | HTTP 代理 URL | http://proxy.com:8080 |
| HTTPS_PROXY | HTTPS 代理 URL | http://proxy.com:8080 |
| NO_PROXY | 不使用代理的主机 | localhost,127.0.0.1 |
| http_proxy | HTTP 代理 URL (小写) | http://proxy.com:8080 |
| https_proxy | HTTPS 代理 URL (小写) | http://proxy.com:8080 |
| no_proxy | 不使用代理的主机 (小写) | localhost,127.0.0.1 |

## 安全注意事项

1. **避免在命令行中暴露密码**: 使用环境变量或配置文件
2. **使用 HTTPS 代理**: 确保代理连接的安全性
3. **限制 NO_PROXY 范围**: 只添加必要的内网地址
4. **定期更新代理配置**: 确保使用最新的代理设置

## 支持

如果遇到问题，请检查：
1. 代理服务器是否正常工作
2. 网络连接是否稳定
3. Docker 版本是否兼容
4. 构建日志中的具体错误信息
