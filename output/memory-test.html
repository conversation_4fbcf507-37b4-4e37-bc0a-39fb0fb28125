
<!DOCTYPE html>
<html>
<head>
    <title>WASM 内存测试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>OnlyOffice X2T WASM 内存测试</h1>
    <div id="status">正在加载...</div>
    <div id="memory-info"></div>
    <button onclick="testMemory()">测试内存</button>
    <button onclick="testConversion()">测试转换</button>
    
    <script src="x2t.js"></script>
    <script>
        let moduleReady = false;
        
        Module = {
            onRuntimeInitialized: function() {
                moduleReady = true;
                document.getElementById('status').innerHTML = '✅ WASM 模块已加载';
                updateMemoryInfo();
            },
            print: function(text) {
                console.log('WASM:', text);
            },
            printErr: function(text) {
                console.error('WASM Error:', text);
            }
        };
        
        function updateMemoryInfo() {
            if (typeof checkWasmMemory === 'function') {
                const info = checkWasmMemory();
                if (info) {
                    document.getElementById('memory-info').innerHTML = 
                        '📊 内存使用: ' + info.mb + ' MB (' + info.bytes.toLocaleString() + ' bytes)';
                }
            }
        }
        
        function testMemory() {
            if (!moduleReady) {
                alert('WASM 模块尚未加载完成');
                return;
            }
            
            updateMemoryInfo();
            
            // 尝试分配一些内存来测试
            try {
                const testSize = 1024 * 1024; // 1MB
                const ptr = Module._malloc(testSize);
                if (ptr) {
                    console.log('✅ 成功分配', testSize, '字节内存');
                    Module._free(ptr);
                    console.log('✅ 内存已释放');
                } else {
                    console.error('❌ 内存分配失败');
                }
            } catch (e) {
                console.error('❌ 内存测试失败:', e);
            }
            
            updateMemoryInfo();
        }
        
        function testConversion() {
            if (!moduleReady) {
                alert('WASM 模块尚未加载完成');
                return;
            }
            
            // 这里可以添加实际的文档转换测试
            console.log('🧪 开始转换测试...');
            alert('转换测试功能需要根据具体的 API 实现');
        }
        
        // 定期更新内存信息
        setInterval(updateMemoryInfo, 5000);
    </script>
</body>
</html>
